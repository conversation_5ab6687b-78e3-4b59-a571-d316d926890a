import { logging, measures } from "@skywind-group/sw-utils";
import { DomainDetector, getAdapter } from "../services/domainDetector/domainDetector";
import { loadDomains } from "../services/domainDetector/loadDomains";
import { CronJobFn } from "../utils/cronJob";

const log = logging.logger("domain-detector-job");

export class DomainDetectorJob {
    private readonly detectors = new Map<string, DomainDetector>();

    public async fire() {
        await measures.measureProvider.runInTransaction("Domain detector job", async () => {
            try {
                await this.doWork();
            } catch (err) {
                measures.measureProvider.saveError(err);
                log.error(err, "Error on executing domain detector job");
            }
        });
    }

    public async doWork(): Promise<void> {
        const items = await loadDomains();
        for (const [adapterId, domains] of items.entries()) {
            const adapter = this.getDomainDetector(adapterId);
            await adapter.init(domains.map(d => d.domain));
        }
    }

    private getDomainDetector(adapter?: string) {
        let item: DomainDetector;
        if (adapter) {
            item = this.detectors.get(adapter);
            if (!item) {
                item = new DomainDetector(getAdapter(adapter));
                this.detectors.set(adapter, item);
            }
        }
        return item;
    }
}

export const domainDetectorJob: CronJobFn = async () => {
}
