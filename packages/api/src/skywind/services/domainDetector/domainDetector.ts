import { Models } from "../../models/models";
import { getStaticDomainPoolService } from "../staticDomainPool";
import { AccessStatus, getTapkingAdapter } from "./tapkingAdapter";
import { DomainDetectorAdapter } from "./types";

export function getAdapter(adapter: string): DomainDetectorAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter();
    }
    throw new Error(`Domain detector adapter ${adapter} is not supported.`);
}

interface DomainSource {
    model: string;
    entityId?: number;
    poolId?: number;
    domainId?: number;
}

interface DomainStatus {
    source?: DomainSource;
    status: AccessStatus;
}

export class DomainDetector {
    private readonly domains: Map<string, DomainStatus> = new Map();

    constructor(private readonly adapter: DomainDetectorAdapter) {
    }

    public async init(domains: Map<string, Omit<DomainStatus, "status">>) {
        const list = await this.adapter.list();
        for (const { domain, statuses: [{ accessStatus = AccessStatus.UNKNOWN } = {}] } of list) {
            this.domains.set(domain, { status: accessStatus });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { source }] of domains.entries()) {
            if (!this.domains.has(domain)) {
                await this.adapter.register(domain);
                this.domains.set(domain, { source, status: AccessStatus.UNKNOWN });
            }
        }
    }

    public async check() {
        for (const [domain, { source }] of this.domains.entries()) {
            const { statuses } = await this.adapter.get(domain);
            if (statuses.length) {
                const status = statuses[0].accessStatus;
                this.domains.set(domain, { source, status });
                if (status === AccessStatus.BLOCKED) {
                    await this.blocked(source);
                    await this.adapter.remove(domain);
                    this.domains.delete(domain);
                }
            }
        }
    }

    private async blocked({ model, entityId, poolId, domainId }: DomainSource) {
        if (model === "pool" && poolId && domainId) {
            getStaticDomainPoolService().disableDomain(poolId, domainId);
        }
        if (entityId) {
            if (model === "staticDomainId") {
                await Models.EntityModel.update({ staticDomainId: null }, { where: { id: entityId } });
            }
        }
    }
}

export function getDomainDetector(adapter: string): DomainDetector {
    return new DomainDetector(getAdapter(adapter));
}
