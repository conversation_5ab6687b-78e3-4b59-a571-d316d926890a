import { Op } from "sequelize";
import { Models } from "../../models/models";
import { EntityStatus } from "../../entities/entity";

interface DomainSource {
    model: string;
    entityId?: number;
    poolId?: number;
    domainId?: number;
    domainDetectorAdapterId?: string;
}

interface DomainInfo {
    id: number;
    domain: string;
    sources: DomainSource[];
}

interface StaticDomainData {
    id: number;
    domain: string;
}

interface StaticDomainPoolData {
    id: number;
    domainDetectorAdapterId: string;
    domains?: Array<StaticDomainData & {
        StaticDomainPoolItem?: {
            isActive?: boolean;
        };
    }>;
}

interface EntityWithDomains {
    id: number;
    staticDomainId?: number;
    lobbyDomainId?: number;
    liveStreamingDomainId?: number;
    ehubDomainId?: number;
    StaticDomainPoolModel: StaticDomainPoolData;
    staticDomain?: StaticDomainData;
    lobbyDomain?: StaticDomainData;
    liveStreamingDomain?: StaticDomainData;
    ehubDomain?: StaticDomainData;
}

export async function loadDomains(): Promise<Map<string, DomainInfo[]>> {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainDetectorAdapterId"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });
    const items = new Map<string, DomainInfo>();
    for (const entity of entities) {
        toDomains(entity as unknown as EntityWithDomains, items);
    }
    const adapters = new Map<string, DomainInfo[]>();
    for (const item of items.values()) {
        for (const { domainDetectorAdapterId } of item.sources) {
            if (domainDetectorAdapterId) {
                adapters.set(domainDetectorAdapterId, [
                    ...(adapters.get(domainDetectorAdapterId) ?? []),
                    item
                ]);
            }
        }
    }
    return adapters;
}

function toDomains(entity: EntityWithDomains, items: Map<string, DomainInfo>): void {
    const addDomain = (id: number, domain: string, source: DomainSource) => {
        items.set(domain, {
            id,
            domain,
            sources: [source, ...items.get(domain)?.sources ?? []]
        });
    };

    const { id: entityId, StaticDomainPoolModel: pool, staticDomain, lobbyDomain, liveStreamingDomain, ehubDomain } = entity;

    if (Array.isArray(pool.domains) && pool.domains.length > 0) {
        for (const domain of pool.domains) {
            const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
            if (isActive) {
                addDomain(domain.id, domain.domain, {
                    model: "pool",
                    poolId: pool.id,
                    domainId: domain.id,
                    domainDetectorAdapterId: pool.domainDetectorAdapterId
                });
            }
        }
    }
    if (staticDomain) {
        addDomain(staticDomain.id, staticDomain.domain, { model: "staticDomainId", entityId });
    }
    if (lobbyDomain) {
        addDomain(lobbyDomain.id, lobbyDomain.domain, { model: "lobbyDomainId", entityId });
    }
    if (liveStreamingDomain) {
        addDomain(liveStreamingDomain.id, liveStreamingDomain.domain, { model: "liveStreamingDomainId", entityId });
    }
    if (ehubDomain) {
        addDomain(ehubDomain.id, ehubDomain.domain, { model: "ehubDomainId", entityId });
    }
}
