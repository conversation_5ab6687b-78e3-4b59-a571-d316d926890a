enum AccessStatus {
    AVAILABLE = "AVAILABLE",
    BLOCKED = "BLOCKED",
    UNKNOWN = "UNKNOWN"
}

interface DomainStatus {
    region?: string;
    accessStatus: AccessStatus;
    lastCheckedAt?: string | null;
}

export interface MonitoredDomain {
    domain: string;
    statuses: DomainStatus[];
}

export interface DomainDetectorAdapter {
    register(domain: string): Promise<MonitoredDomain>;
    remove(domain: string): Promise<void>;
    get(domain: string): Promise<MonitoredDomain>;
    list(): Promise<MonitoredDomain[]>;
}
