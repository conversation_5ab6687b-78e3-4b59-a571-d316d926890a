# Tapking API Scripts

This folder contains curl scripts for interacting with the Tapking domain block detector API.

## Setup

Before running these scripts, you need to set the `TAPKING_TOKEN` environment variable with your API token:

```bash
export TAPKING_TOKEN="your_api_token_here"
```

Or add it to your shell profile (`.bashrc`, `.zshrc`, etc.):

```bash
echo 'export TAPKING_TOKEN="your_api_token_here"' >> ~/.bashrc
source ~/.bashrc
```

## Available Scripts

- `delete.http` - Delete a domain from monitoring
- `domain.http` - Get status for a specific domain
- `domains.http` - Get status for all domains
- `register.http` - Register a new domain for monitoring

## Usage

After setting the environment variable, you can run the scripts directly:

```bash
# Make the files executable (if needed)
chmod +x *.http

# Run a script
bash delete.http
bash domain.http
bash domains.http
bash register.http
```

Or copy and paste the curl commands from the files into your terminal.
